import pandas as pd
import torch
from sklearn.model_selection import train_test_split
from transformers import BertTokenizer, BertForSequenceClassification, Trainer, TrainingArguments
from transformers import TrainingArguments

# Step 1: Load Excel data
df = pd.read_excel("/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/BERTO-DEMO/archive_list_inal.xlsx", sheet_name="MergedSheet")

# Step 2: Use required columns and clean
df = df[["labor_description_str", "category_num"]].dropna()

train_texts, val_texts, train_labels, val_labels = train_test_split(
    df["labor_description_str"].astype(str).tolist(),
    df["category_num"].tolist(),
    test_size=0.2,
    random_state=42
)

# Step 4: Tokenize
tokenizer = BertTokenizer.from_pretrained("bert-base-uncased")

train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=128)
val_encodings = tokenizer(val_texts, truncation=True, padding=True, max_length=128)

# Step 5: Dataset class
class BERTDataset(torch.utils.data.Dataset):
    def __init__(self, encodings, labels):
        self.encodings = encodings
        self.labels = labels

    def __getitem__(self, idx):
        item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
        item["labels"] = torch.tensor(self.labels[idx])
        return item

    def __len__(self):
        return len(self.labels)

train_dataset = BERTDataset(train_encodings, train_labels)
val_dataset = BERTDataset(val_encodings, val_labels)

# Step 6: Load model
num_labels = df["category_num"].nunique()
model = BertForSequenceClassification.from_pretrained("bert-base-uncased", num_labels=num_labels)

# Step 7: Training arguments
training_args = TrainingArguments(
    output_dir="./bert-output",
    evaluation_strategy="epoch",
    save_strategy="epoch",
    per_device_train_batch_size=8,
    per_device_eval_batch_size=8,
    num_train_epochs=3,
    weight_decay=0.01,
    logging_dir="./logs",
    logging_steps=10,
)

# Step 8: Trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=val_dataset,
)

# Step 9: Train
trainer.train()

# Optional: Evaluate
metrics = trainer.evaluate()
print(metrics)
